<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <!-- App Sandbox is required for Mac App Store -->
    <key>com.apple.security.app-sandbox</key>
    <true/>

    <!-- Network access for API calls -->
    <key>com.apple.security.network.client</key>
    <true/>

    <!-- Microphone access for voice dictation -->
    <key>com.apple.security.device.microphone</key>
    <true/>

    <!-- Audio input access -->
    <key>com.apple.security.device.audio-input</key>
    <true/>

    <!-- File access for saving transcriptions -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>

    <!-- Downloads folder access -->
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>

    <!-- Apple Events for system integration -->
    <key>com.apple.security.automation.apple-events</key>
    <true/>

    <!-- JIT compilation allowed for Electron/Node.js -->
    <key>com.apple.security.cs.allow-jit</key>
    <true/>

    <!-- Allow unsigned executable memory for Electron/Node.js -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>

    <!-- Allow DYLD environment variables for Electron/Node.js -->
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>


  </dict>
</plist>
