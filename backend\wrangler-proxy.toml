# API Proxy Worker Configuration
name = "speakmcp-proxy"
main = "src/proxy.ts"

# Inherit from base wrangler.toml
compatibility_date = "2024-01-01"

# D1 Database
[[d1_databases]]
binding = "DB"
database_name = "speakmcp-db"
database_id = "3347842c-06c0-4f89-adc5-b91cfadd990e"

# Custom domain for proxy worker
[route]
pattern = "proxy.speakmcp.techfren.net"
custom_domain = true

# Environment Variables for local development
[vars]
JWT_SECRET = "test-jwt-secret-for-local-development-only-not-secure"
GROQ_API_KEY = "your-groq-api-key-from-env-local"
ALLOWED_ORIGINS = "*"

[env.development.vars]

[env.production.vars]
JWT_SECRET = "your-jwt-secret"
GROQ_API_KEY = "your-groq-api-key"
ALLOWED_ORIGINS = "https://speakmcp.app"
