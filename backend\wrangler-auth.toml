# Authentication Worker Configuration
name = "speakmcp-auth"
main = "src/auth.ts"

# Inherit from base wrangler.toml
compatibility_date = "2024-01-01"

# D1 Database
[[d1_databases]]
binding = "DB"
database_name = "speakmcp-db"
database_id = "3347842c-06c0-4f89-adc5-b91cfadd990e"

# Custom domain for auth worker
[route]
pattern = "auth.speakmcp.techfren.net"
custom_domain = true

# Environment Variables for local development
[vars]
JWT_SECRET = "totalle0raandoamsint"
GOOGLE_CLIENT_ID = "1076341506802-i4at7nlm0vlkol1gnv46ch27qsd457ef.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "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"
ALLOWED_ORIGINS = "*"

[env.development.vars]

[env.production.vars]
JWT_SECRET = "your-jwt-secret"
GOOGLE_CLIENT_ID = "your-google-client-id"
GOOGLE_CLIENT_SECRET = "your-google-client-secret"
ALLOWED_ORIGINS = "https://speakmcp.app"
