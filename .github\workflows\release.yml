name: Build/release

on: push

jobs:
  release:
    runs-on: ${{ matrix.os }}

    if: |
      startsWith(github.ref, 'refs/tags/v') ||
      contains(github.event.head_commit.message, '[release]')

    strategy:
      matrix:
        os: [macos-latest, windows-latest]

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install setuptools
        if: matrix.os == 'macos-latest'
        run: brew install python-setuptools

      - uses: actions-rust-lang/setup-rust-toolchain@v1

      - name: Install pnpm
        run: npm i -g pnpm@9

      - name: Fix pnpm
        run: pnpm fix-pnpm-windows

      - name: Get pnpm cache directory path
        id: pnpm-cache-dir-path
        shell: "bash"
        run: echo "dir=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        id: pnpm-cache
        with:
          path: ${{ steps.pnpm-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install deps
        run: pnpm i

      - name: Release
        run: pnpm run release
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_APP_SPECIFIC_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
          CSC_LINK: ${{ secrets.CSC_LINK }}
          GITHUB_TOKEN: ${{ secrets.GH_PUBLISH_TOKEN }}
          # https://github.com/electron-userland/electron-builder/issues/3179
          USE_HARD_LINKS: false
