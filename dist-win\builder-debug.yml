x64:
  firstOrDefaultFilePatterns:
    - '!**/node_modules'
    - '!build{,/**/*}'
    - '!dist-win{,/**/*}'
    - out/**/*
    - node_modules/**/*
    - package.json
    - '!**/.vscode/*'
    - '!src/*'
    - '!scripts/*'
    - '!electron.vite.config.{js,ts,mjs,cjs}'
    - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
    - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
    - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
    - '!*.{js,cjs,mjs,ts}'
    - '!components.json'
    - '!.prettierrc'
    - '!speakmcp-rs/*'
    - resources/**
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - out/**/*
    - node_modules/**/*
    - package.json
    - '!**/.vscode/*'
    - '!src/*'
    - '!scripts/*'
    - '!electron.vite.config.{js,ts,mjs,cjs}'
    - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
    - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
    - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
    - '!*.{js,cjs,mjs,ts}'
    - '!components.json'
    - '!.prettierrc'
    - '!speakmcp-rs/*'
    - resources/**
