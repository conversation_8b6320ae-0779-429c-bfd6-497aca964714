#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🪟 Creating manual Windows build...');

const projectRoot = process.cwd();
const buildDir = path.join(projectRoot, 'manual-build-win');
const electronVersion = '31.7.0';

// Clean build directory
if (fs.existsSync(buildDir)) {
    console.log('🧹 Cleaning existing build directory...');
    fs.rmSync(buildDir, { recursive: true, force: true });
}

fs.mkdirSync(buildDir, { recursive: true });

console.log('📦 Downloading Electron...');

// Download Electron for Windows
const electronUrl = `https://github.com/electron/electron/releases/download/v${electronVersion}/electron-v${electronVersion}-win32-x64.zip`;
const electronZip = path.join(buildDir, 'electron.zip');

try {
    execSync(`powershell -Command "Invoke-WebRequest -Uri '${electronUrl}' -OutFile '${electronZip}'"`, { 
        stdio: 'inherit',
        cwd: buildDir 
    });
    
    console.log('📂 Extracting Electron...');
    execSync(`powershell -Command "Expand-Archive -Path '${electronZip}' -DestinationPath '${buildDir}' -Force"`, { 
        stdio: 'inherit' 
    });
    
    // Remove the zip file
    fs.unlinkSync(electronZip);
    
    console.log('📋 Copying application files...');
    
    // Copy built application files
    const appDir = path.join(buildDir, 'resources', 'app');
    fs.mkdirSync(appDir, { recursive: true });
    
    // Copy package.json
    fs.copyFileSync(
        path.join(projectRoot, 'package.json'),
        path.join(appDir, 'package.json')
    );
    
    // Copy built output
    const outDir = path.join(projectRoot, 'out');
    if (fs.existsSync(outDir)) {
        copyDirectory(outDir, path.join(appDir, 'out'));
    } else {
        console.error('❌ Built output not found. Please run "npx electron-vite build" first.');
        process.exit(1);
    }
    
    // Copy node_modules (only production dependencies)
    console.log('📦 Installing production dependencies...');
    execSync('npm install --production --no-optional', { 
        stdio: 'inherit',
        cwd: appDir 
    });
    
    // Copy resources
    const resourcesDir = path.join(projectRoot, 'resources');
    if (fs.existsSync(resourcesDir)) {
        copyDirectory(resourcesDir, path.join(appDir, 'resources'));
    }
    
    // Rename electron.exe to speakmcp.exe
    const electronExe = path.join(buildDir, 'electron.exe');
    const appExe = path.join(buildDir, 'speakmcp.exe');
    if (fs.existsSync(electronExe)) {
        fs.renameSync(electronExe, appExe);
    }
    
    console.log('✅ Manual Windows build completed!');
    console.log(`📁 Build location: ${buildDir}`);
    console.log(`🚀 Run: ${appExe}`);
    
} catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
}

function copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}
