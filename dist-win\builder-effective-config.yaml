directories:
  output: dist-win
  buildResources: build
appId: app.speakmcp
productName: SpeakMCP
files:
  - filter:
      - out/**/*
      - node_modules/**/*
      - package.json
      - '!**/.vscode/*'
      - '!src/*'
      - '!scripts/*'
      - '!electron.vite.config.{js,ts,mjs,cjs}'
      - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
      - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
      - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
      - '!*.{js,cjs,mjs,ts}'
      - '!components.json'
      - '!.prettierrc'
      - '!speakmcp-rs/*'
      - resources/**
asarUnpack:
  - resources/**
  - node_modules/**
extraFiles:
  - from: resources/bin/
    to: Resources/app.asar.unpacked/resources/bin/
    filter:
      - '**/*'
win:
  executableName: speakmcp
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  extraFiles:
    - from: resources/bin/speakmcp-rs.exe
      to: resources/bin/speakmcp-rs.exe
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  artifactName: ${productName}-${version}-${arch}.${ext}
  entitlementsInherit: build/entitlements.mac.plist
  entitlements: build/entitlements.mac.plist
  hardenedRuntime: true
  gatekeeperAssess: false
  identity: Developer ID Application
  category: public.app-category.productivity
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
    - target: pkg
      arch:
        - x64
        - arm64
  extendInfo:
    NSCameraUsageDescription: SpeakMCP may request camera access for enhanced AI features.
    NSMicrophoneUsageDescription: SpeakMCP requires microphone access for voice dictation and transcription.
    NSDocumentsFolderUsageDescription: SpeakMCP may access your Documents folder to save transcriptions and settings.
    NSDownloadsFolderUsageDescription: SpeakMCP may access your Downloads folder to save exported files.
    LSMinimumSystemVersion: 12.0.0
    CFBundleURLTypes:
      - CFBundleURLName: SpeakMCP Protocol
        CFBundleURLSchemes:
          - speakmcp
  notarize: false
mas:
  artifactName: ${productName}-${version}-mas.${ext}
  entitlementsInherit: build/entitlements.mas.inherit.plist
  entitlements: build/entitlements.mas.plist
  hardenedRuntime: false
  identity: 3rd Party Mac Developer Application
  category: public.app-category.productivity
  type: distribution
  preAutoEntitlements: false
  extendInfo:
    NSCameraUsageDescription: SpeakMCP may request camera access for enhanced AI features.
    NSMicrophoneUsageDescription: SpeakMCP requires microphone access for voice dictation and transcription.
    NSDocumentsFolderUsageDescription: SpeakMCP may access your Documents folder to save transcriptions and settings.
    NSDownloadsFolderUsageDescription: SpeakMCP may access your Downloads folder to save exported files.
    LSMinimumSystemVersion: 12.0.0
    CFBundleURLTypes:
      - CFBundleURLName: SpeakMCP Protocol
        CFBundleURLSchemes:
          - speakmcp
masDev:
  artifactName: ${productName}-${version}-mas-dev.${ext}
  entitlementsInherit: build/entitlements.mas.inherit.plist
  entitlements: build/entitlements.mas.plist
  hardenedRuntime: false
  identity: Mac Developer
  category: public.app-category.productivity
  extendInfo:
    NSCameraUsageDescription: SpeakMCP may request camera access for enhanced AI features.
    NSMicrophoneUsageDescription: SpeakMCP requires microphone access for voice dictation and transcription.
    NSDocumentsFolderUsageDescription: SpeakMCP may access your Documents folder to save transcriptions and settings.
    NSDownloadsFolderUsageDescription: SpeakMCP may access your Downloads folder to save exported files.
    LSMinimumSystemVersion: 10.15.0
    CFBundleURLTypes:
      - CFBundleURLName: SpeakMCP Protocol
        CFBundleURLSchemes:
          - speakmcp
dmg:
  artifactName: ${productName}-${version}-${arch}.${ext}
pkg:
  artifactName: ${productName}-${version}-${arch}.${ext}
  identity: Developer ID Application
  allowAnywhere: false
  allowCurrentUserHome: false
  allowRootDirectory: false
  isRelocatable: false
  overwriteAction: upgrade
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: github
  owner: aj47
  repo: SpeakMCP
removePackageScripts: true
electronVersion: 31.7.0
