#!/bin/bash

# SpeakMCP Apple Developer Setup Script
# This script helps you set up code signing and notarization for macOS distribution

echo "🍎 SpeakMCP Apple Developer Setup"
echo "=================================="
echo ""

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script must be run on macOS"
    exit 1
fi

echo "📋 Step 1: Get your Apple Developer Team ID"
echo "1. Go to https://developer.apple.com/account/"
echo "2. Sign <NAME_EMAIL>"
echo "3. Look for 'Team ID' in the membership section"
echo "4. It should be a 10-character alphanumeric string (e.g., ABC123DEF4)"
echo ""
read -p "Enter your Team ID: " TEAM_ID

echo ""
echo "📋 Step 2: Generate App-Specific Password"
echo "1. Go to https://appleid.apple.com/account/manage"
echo "2. Sign <NAME_EMAIL>"
echo "3. Go to 'Sign-In and Security' > 'App-Specific Passwords'"
echo "4. Click '+' to generate a new password"
echo "5. Enter 'SpeakMCP Notarization' as the label"
echo "6. Copy the generated password (format: xxxx-xxxx-xxxx-xxxx)"
echo ""
read -p "Enter your App-Specific Password: " APP_PASSWORD

echo ""
echo "📋 Step 3: Install Developer Certificate"
echo "1. Go to https://developer.apple.com/account/resources/certificates/list"
echo "2. Click '+' to create a new certificate"
echo "3. Select 'Developer ID Application' (for distribution outside Mac App Store)"
echo "4. Follow the instructions to create a Certificate Signing Request (CSR)"
echo "5. Download and install the certificate in Keychain Access"
echo ""
echo "After installing the certificate, run this command to find your signing identity:"
echo "security find-identity -v -p codesigning"
echo ""
read -p "Press Enter after you've installed the certificate..."

echo ""
echo "🔍 Finding your code signing identity..."
SIGNING_IDENTITIES=$(security find-identity -v -p codesigning | grep "Developer ID Application")

if [ -z "$SIGNING_IDENTITIES" ]; then
    echo "❌ No Developer ID Application certificates found!"
    echo "Please make sure you've installed your certificate correctly."
    exit 1
fi

echo "Found signing identities:"
echo "$SIGNING_IDENTITIES"
echo ""

# Extract the first identity name
IDENTITY_NAME=$(echo "$SIGNING_IDENTITIES" | head -n1 | sed 's/.*"\(.*\)".*/\1/')

echo "📝 Updating .env file..."

# Update the .env file
cat > .env << EOF
# Apple Developer credentials for code signing and notarization
# Generated by setup-apple-signing.sh

# Your Apple Developer Team ID
APPLE_TEAM_ID=$TEAM_ID

# Your Apple ID
APPLE_ID=<EMAIL>

# App-specific password for notarization
APPLE_APP_SPECIFIC_PASSWORD=$APP_PASSWORD

# Code signing identity
CSC_NAME="$IDENTITY_NAME"
EOF

echo "✅ .env file updated successfully!"
echo ""
echo "🚀 Next steps:"
echo "1. Run 'npm run build:mac' to build and sign your app"
echo "2. The app will be automatically notarized if all credentials are correct"
echo "3. Check the dist/ folder for your signed .dmg file"
echo ""
echo "🔧 Troubleshooting:"
echo "- If signing fails, check that your certificate is installed in Keychain Access"
echo "- If notarization fails, verify your App-Specific Password is correct"
echo "- Make sure your Apple Developer account has the necessary permissions"
